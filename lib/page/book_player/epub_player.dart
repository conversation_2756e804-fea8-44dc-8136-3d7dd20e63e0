import 'dart:async';
import 'dart:convert';

import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/config/shared_preference_provider.dart';
import 'package:dasso_reader/dao/book.dart';
import 'package:dasso_reader/dao/book_note.dart';
import 'package:dasso_reader/dao/theme.dart';
import 'package:dasso_reader/enums/reading_info.dart';
import 'package:dasso_reader/main.dart';
import 'package:dasso_reader/models/book.dart';
import 'package:dasso_reader/models/bookmark.dart';
import 'package:dasso_reader/models/book_style.dart';
import 'package:dasso_reader/models/font_model.dart';
import 'package:dasso_reader/models/read_theme.dart';
import 'package:dasso_reader/models/reading_rules.dart';
import 'package:dasso_reader/models/search_result_model.dart';
import 'package:dasso_reader/models/toc_item.dart';
import 'package:dasso_reader/providers/bookmark.dart';
import 'package:dasso_reader/page/book_player/image_viewer.dart';
import 'package:dasso_reader/page/home_page.dart';
import 'package:dasso_reader/page/reading_page.dart';
import 'package:dasso_reader/providers/book_list.dart';
import 'package:dasso_reader/service/book_player/book_player_server.dart';
import 'package:dasso_reader/service/dictionary/chinese_segmentation_service.dart';
import 'package:dasso_reader/utils/coordinates_to_part.dart';
import 'package:dasso_reader/utils/js/convert_dart_color_to_js.dart';
import 'package:dasso_reader/models/book_note.dart';
import 'package:dasso_reader/utils/webView/gererate_url.dart';
import 'package:dasso_reader/utils/accessibility/semantic_helpers.dart';
import 'package:dasso_reader/utils/log/common.dart';
import 'package:dasso_reader/utils/webView/webview_console_message.dart';
import 'package:dasso_reader/utils/webView/webview_initial_variable.dart';
import 'package:dasso_reader/widgets/bookshelf/book_cover.dart';
import 'package:dasso_reader/widgets/context_menu/unified_context_menu.dart';
import 'package:dasso_reader/widgets/reading_page/more_settings/page_turning/diagram.dart';
import 'package:dasso_reader/widgets/reading_page/more_settings/page_turning/types_and_icons.dart';
import 'package:dasso_reader/widgets/reading_page/style_widget.dart';
import 'package:battery_plus/battery_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:icons_plus/icons_plus.dart';
import 'package:intl/intl.dart';

class EpubPlayer extends ConsumerStatefulWidget {
  final Book book;
  final String? cfi;
  final Function showOrHideAppBarAndBottomBar;
  final Function onLoadEnd;
  final Function updateParent;

  const EpubPlayer({
    super.key,
    required this.showOrHideAppBarAndBottomBar,
    required this.book,
    this.cfi,
    required this.onLoadEnd,
    required this.updateParent,
  });

  @override
  ConsumerState<EpubPlayer> createState() => EpubPlayerState();
}

class EpubPlayerState extends ConsumerState<EpubPlayer>
    with TickerProviderStateMixin {
  late InAppWebViewController webViewController;
  late ContextMenu contextMenu;
  String cfi = '';
  double percentage = 0.0;
  String chapterTitle = '';
  String chapterHref = '';
  int chapterCurrentPage = 0;
  int chapterTotalPages = 0;
  List<TocItem> toc = [];
  OverlayEntry? contextMenuEntry;
  AnimationController? _animationController;
  Animation<double>? _animation;
  double searchProcess = 0.0;
  List<SearchResultModel> searchResult = [];
  bool showHistory = false;
  bool canGoBack = false;
  bool canGoForward = false;
  late Book book;
  String? backgroundColor;
  String? textColor;
  Timer? styleTimer;
  String bookmarkCfi = '';
  bool bookmarkExists = false;

  final StreamController<double> _searchProgressController =
      StreamController<double>.broadcast();

  Stream<double> get searchProgressStream => _searchProgressController.stream;

  final StreamController<List<SearchResultModel>> _searchResultController =
      StreamController<List<SearchResultModel>>.broadcast();

  Stream<List<SearchResultModel>> get searchResultStream =>
      _searchResultController.stream;

  FocusNode focusNode = FocusNode();

  void prevPage() {
    webViewController.evaluateJavascript(source: 'prevPage()');
  }

  void nextPage() {
    webViewController.evaluateJavascript(source: 'nextPage()');
  }

  void prevChapter() {
    webViewController.evaluateJavascript(
      source: '''
      prevSection()
      ''',
    );
  }

  void nextChapter() {
    // Use non-blocking JavaScript execution
    webViewController.evaluateJavascript(
      source: '''
      requestAnimationFrame(() => {
        nextSection();
      });
      ''',
    );
  }

  Future<void> goToPercentage(double value) async {
    // Use non-blocking JavaScript execution with debouncing
    await webViewController.evaluateJavascript(
      source: '''
      if (window.goToPercentDebounce) clearTimeout(window.goToPercentDebounce);
      window.goToPercentDebounce = setTimeout(() => {
        goToPercent($value);
      }, 16); // 60fps debouncing
      ''',
    );
  }

  void changeTheme(ReadTheme readTheme) {
    textColor = readTheme.textColor;
    backgroundColor = readTheme.backgroundColor;

    String bc = convertDartColorToJs(readTheme.backgroundColor);
    String tc = convertDartColorToJs(readTheme.textColor);

    // Use non-blocking theme change with requestAnimationFrame
    webViewController.evaluateJavascript(
      source: '''
      requestAnimationFrame(() => {
        changeStyle({
          backgroundColor: '#$bc',
          fontColor: '#$tc',
        });
      });
      ''',
    );
  }

  void changeStyle(BookStyle bookStyle) {
    styleTimer?.cancel();
    styleTimer = Timer(DesignSystem.durationMedium, () {
      webViewController.evaluateJavascript(
        source: '''
      changeStyle({
        fontSize: ${bookStyle.fontSize},
        spacing: ${bookStyle.lineHeight},
        fontWeight: ${bookStyle.fontWeight},
        paragraphSpacing: ${bookStyle.paragraphSpacing},
        topMargin: ${bookStyle.topMargin},
        bottomMargin: ${bookStyle.bottomMargin},
        sideMargin: ${bookStyle.sideMargin},
        letterSpacing: ${bookStyle.letterSpacing},
        textIndent: ${bookStyle.indent},
        maxColumnCount: ${bookStyle.maxColumnCount},
      })
      ''',
      );
    });
  }

  void changeReadingRules(ReadingRules readingRules) {
    webViewController.evaluateJavascript(
      source: '''
      readingFeatures({
        convertChineseMode: '${readingRules.convertChineseMode.name}',
        bionicReadingMode: ${readingRules.bionicReading},
      })
    ''',
    );
  }

  void changeFont(FontModel font) {
    webViewController.evaluateJavascript(
      source: '''
      changeStyle({
        fontName: '${font.name}',
        fontPath: '${font.path}',
      })
    ''',
    );
  }

  void changePageTurnStyle(PageTurn pageTurnStyle) {
    webViewController.evaluateJavascript(
      source: '''
      changeStyle({
        pageTurnStyle: '${pageTurnStyle.name}',
      })
    ''',
    );
  }

  // Text selection mode method removed - only free selection is supported

  void goToHref(String href) =>
      webViewController.evaluateJavascript(source: "goToHref('$href')");

  void goToCfi(String cfi) {
    webViewController.evaluateJavascript(source: "goToCfi('$cfi')");

    // Ensure annotations are visible after navigation
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted) {
        AnxLog.info(
          'Re-rendering annotations after CFI navigation to ensure highlights are visible',
        );
        renderAnnotations(webViewController);
      }
    });
  }

  void addAnnotation(BookNote bookNote) {
    webViewController.evaluateJavascript(
      source: '''
      addAnnotation({
        id: ${bookNote.id},
        type: '${bookNote.type}',
        value: '${bookNote.cfi}',
        color: '#${bookNote.color}',
        note: '${bookNote.content.replaceAll('\n', ' ')}',
      })
      ''',
    );
  }

  void removeAnnotation(String cfi) =>
      webViewController.evaluateJavascript(source: "removeAnnotation('$cfi')");

  void addBookmark(BookmarkModel bookmark) {
    webViewController.evaluateJavascript(
      source: '''
      addAnnotation({
        id: ${bookmark.id},
        type: 'bookmark',
        value: '${bookmark.cfi}',
        color: '#000000',
        note: 'None',
      })
      ''',
    );
  }

  void addBookmarkHere() {
    webViewController.evaluateJavascript(
      source: '''
      addBookmarkHere()
      ''',
    );
  }

  void clearSearch() {
    webViewController.evaluateJavascript(source: 'clearSearch()');
    searchResult.clear();
    _searchResultController.add(searchResult);
  }

  void search(String text) {
    clearSearch();
    webViewController.evaluateJavascript(
      source: '''
      search('$text', {
        'scope': 'book',
        'matchCase': false,
        'matchDiacritics': false,
        'matchWholeWords': false,
      })
    ''',
    );
  }

  Future<void> initTts() async =>
      await webViewController.evaluateJavascript(source: 'window.ttsHere()');

  void ttsStop() => webViewController.evaluateJavascript(source: 'ttsStop()');

  Future<String> ttsNext() async {
    final result = await webViewController.callAsyncJavaScript(
      functionBody: 'return await ttsNext()',
    );
    return (result?.value as String?) ?? '';
  }

  Future<String> ttsPrev() async {
    final result = await webViewController.callAsyncJavaScript(
      functionBody: 'return await ttsPrev()',
    );
    return (result?.value as String?) ?? '';
  }

  Future<String> ttsPrevSection() async {
    final result = await webViewController.callAsyncJavaScript(
      functionBody: 'return await ttsPrevSection()',
    );
    return (result?.value as String?) ?? '';
  }

  Future<String> ttsNextSection() async {
    final result = await webViewController.callAsyncJavaScript(
      functionBody: 'return await ttsNextSection()',
    );
    return (result?.value as String?) ?? '';
  }

  Future<String> ttsPrepare() async {
    final result =
        await webViewController.evaluateJavascript(source: 'ttsPrepare()');
    return (result as String?) ?? '';
  }

  Future<bool> isFootNoteOpen() async {
    final result = await webViewController.evaluateJavascript(
      source: 'window.isFootNoteOpen()',
    );
    return (result as bool?) ?? false;
  }

  void backHistory() {
    webViewController.evaluateJavascript(source: 'back()');
  }

  void forwardHistory() {
    webViewController.evaluateJavascript(source: 'forward()');
  }

  Future<String> theChapterContent() async {
    final result = await webViewController.evaluateJavascript(
      source: 'theChapterContent()',
    );
    return (result as String?) ?? '';
  }

  Future<String> previousContent(int count) async {
    final result = await webViewController.evaluateJavascript(
      source: 'previousContent($count)',
    );
    return (result as String?) ?? '';
  }

  /// Preemptively process and cache visible Chinese content
  Future<void> preprocessVisibleContent() async {
    try {
      // Get visible text content from the current view
      final result = await webViewController.evaluateJavascript(
        source: '''
          (function() {
            try {
              // Get text from all visible paragraphs and other text elements
              const visibleElements = Array.from(document.querySelectorAll('p, div, span, h1, h2, h3, h4, h5, h6'))
                .filter(el => {
                  // Filter to elements that are visible and contain text
                  const rect = el.getBoundingClientRect();
                  return rect.top >= 0 &&
                         rect.left >= 0 &&
                         rect.bottom <= window.innerHeight &&
                         rect.right <= window.innerWidth &&
                         el.textContent.trim().length > 0;
                });

              return visibleElements.map(el => el.textContent).join('\\n\\n');
            } catch(e) {
              console.error('Error getting visible content:', e);
              return '';
            }
          })();
        ''',
      );
      final String visibleText = (result as String?) ?? '';

      if (visibleText.isNotEmpty) {
        // Check if the text contains Chinese characters
        if (RegExp(r'[\u4e00-\u9fa5]').hasMatch(visibleText)) {
          AnxLog.info(
            'Preprocessing visible Chinese content (${visibleText.length} chars)',
          );

          // Get book ID for caching
          final bookId = widget.book.id;

          // Process in the background to avoid UI lag with lower priority
          Timer(const Duration(milliseconds: 100), () {
            unawaited(_preprocessChineseText(visibleText, bookId));
          });
        } else {
          AnxLog.info('No Chinese characters found in visible content');
        }
      }
    } catch (e) {
      AnxLog.warning('Error preprocessing visible content: $e');
    }
  }

  /// Chinese text preprocessing removed - word boundary detection no longer needed
  Future<void> _preprocessChineseText(String text, int bookId) async {
    // Preprocessing removed since segmentation mode is no longer supported
    AnxLog.info(
      'Chinese text preprocessing skipped - segmentation mode removed',
    );
  }

  /// Store segmentation data for context menu use
  Future<void> _storeSegmentationDataForContextMenu({
    required String selectedText,
    required String fullNodeText,
    required int startOffset,
    required int endOffset,
    required List<int> selectionRange,
  }) async {
    try {
      final segmentationService = ChineseSegmentationService();
      await segmentationService.initialize();

      // Store comprehensive segmentation data for the selected text and surrounding context
      await segmentationService.storeSegmentationDataForSelection(
        selectedText: selectedText,
        fullNodeText: fullNodeText,
        startOffset: startOffset,
        endOffset: endOffset,
        selectionRange: selectionRange,
        bookId: widget.book.id,
      );

      AnxLog.info('Successfully stored segmentation data for context menu');
    } catch (e) {
      AnxLog.severe('Error storing segmentation data for context menu: $e');
    }
  }

  // Text chunking method removed - no longer needed without segmentation preprocessing

  void onClick(Map<String, dynamic> location) {
    readingPageKey.currentState?.resetAwakeTimer();
    if (contextMenuEntry != null) {
      removeOverlay();
      return;
    }
    final double x = (location['x'] as num?)?.toDouble() ?? 0.0;
    final double y = (location['y'] as num?)?.toDouble() ?? 0.0;
    final part = coordinatesToPart(x, y);
    final currentPageTurningType = Prefs().pageTurningType;
    final pageTurningType = pageTurningTypes[currentPageTurningType];
    switch (pageTurningType[part]) {
      case PageTurningType.prev:
        prevPage();
        break;
      case PageTurningType.next:
        nextPage();
        break;
      case PageTurningType.menu:
        widget.showOrHideAppBarAndBottomBar(true);
        break;
    }
  }

  Future<void> renderAnnotations(InAppWebViewController controller) async {
    try {
      List<BookNote> annotationList =
          await selectBookNotesByBookId(widget.book.id);

      AnxLog.info(
        'Rendering ${annotationList.length} annotations for book ${widget.book.title}',
      );

      // Filter out invalid annotations and create safe JSON
      List<Map<String, dynamic>> safeAnnotations = [];

      for (BookNote note in annotationList) {
        try {
          // Validate required fields
          if (note.cfi.isNotEmpty &&
              note.type.isNotEmpty &&
              note.color.isNotEmpty) {
            safeAnnotations.add({
              'id': note.id,
              'note': note.content,
              'value': note.cfi,
              'type': note.type,
              'color': '#${note.color}',
            });
          } else {
            AnxLog.warning(
              'Skipping invalid annotation: id=${note.id}, cfi="${note.cfi}", type="${note.type}", color="${note.color}"',
            );
          }
        } catch (e) {
          AnxLog.warning('Error processing annotation ${note.id}: $e');
        }
      }

      AnxLog.info('Filtered to ${safeAnnotations.length} valid annotations');

      String allAnnotations =
          jsonEncode(safeAnnotations).replaceAll('\'', '\\\'');

      // Use the enhanced method that passes data directly to avoid timing issues
      await controller.evaluateJavascript(
        source: '''
        try {
          const annotationsData = $allAnnotations;
          console.log('Flutter: Rendering', annotationsData.length, 'annotations');

          // Set global variable for compatibility
          window.allAnnotations = annotationsData;

          // Use the enhanced method that accepts data directly
          if (window.reader && window.reader.renderAnnotationWithData) {
            window.reader.renderAnnotationWithData(annotationsData);
          } else {
            // Fallback to original method
            window.renderAnnotations();
          }
        } catch (error) {
          console.error('Error rendering annotations:', error);
        }
      ''',
      );

      AnxLog.info('Annotations rendering completed successfully');
    } catch (e) {
      AnxLog.severe('Error in renderAnnotations: $e');
      // Log more details about the error
      try {
        List<BookNote> annotationList =
            await selectBookNotesByBookId(widget.book.id);
        for (int i = 0; i < annotationList.length; i++) {
          BookNote note = annotationList[i];
          AnxLog.info(
            'Annotation $i: id=${note.id}, cfi=${note.cfi}, type=${note.type}, color=${note.color}, content=${note.content}',
          );
        }
      } catch (debugError) {
        AnxLog.severe('Error during debug logging: $debugError');
      }
    }
  }

  Future<void> getThemeColor() async {
    if (Prefs().autoAdjustReadingTheme) {
      List<ReadTheme> themes = await selectThemes();
      final isDayMode =
          Theme.of(navigatorKey.currentContext!).brightness == Brightness.light;
      backgroundColor =
          isDayMode ? themes[0].backgroundColor : themes[1].backgroundColor;
      textColor = isDayMode ? themes[0].textColor : themes[1].textColor;
    } else {
      backgroundColor = Prefs().readTheme.backgroundColor;
      textColor = Prefs().readTheme.textColor;
    }
    setState(() {});
  }

  Future<void> setHandler(InAppWebViewController controller) async {
    String uri = Uri.encodeComponent(widget.book.fileFullPath);
    String url = 'http://127.0.0.1:${Server().port}/book/$uri';
    String initialCfi = widget.cfi ?? widget.book.lastReadPosition;

    await getThemeColor();

    webviewInitialVariable(
      controller,
      url,
      initialCfi,
      backgroundColor: backgroundColor,
      textColor: textColor,
    );

    // Word boundary handler removed - segmentation mode no longer supported

    // Add handler to store segmentation data for context menu
    controller.addJavaScriptHandler(
      handlerName: 'storeSegmentationData',
      callback: (List<dynamic> args) async {
        try {
          // Enhanced parameter validation
          if (args.isEmpty) {
            AnxLog.warning('storeSegmentationData called with empty arguments');
            return false;
          }

          final Map<String, dynamic> data =
              Map<String, dynamic>.from(args[0] as Map);
          final String selectedText =
              (data['selectedText'] as String? ?? '').trim();
          final String fullNodeText = data['fullNodeText'] as String? ?? '';
          final int startOffset = data['startOffset'] as int? ?? 0;
          final int endOffset = data['endOffset'] as int? ?? 0;
          final List<dynamic> selectionRange =
              data['selectionRange'] as List<dynamic>? ?? [0, 0];

          // Validate input data
          if (selectedText.isEmpty || fullNodeText.isEmpty) {
            AnxLog.warning('Empty text data for segmentation storage');
            return false;
          }

          if (startOffset < 0 || endOffset < 0 || startOffset >= endOffset) {
            AnxLog.warning(
              'Invalid selection offsets: start=$startOffset, end=$endOffset',
            );
            return false;
          }

          if (selectionRange.length < 2) {
            AnxLog.warning('Invalid selection range format');
            return false;
          }

          AnxLog.info(
            'Storing segmentation data for selected text: "$selectedText"',
          );

          // Store the segmentation data for later use in context menu with timeout
          await _storeSegmentationDataForContextMenu(
            selectedText: selectedText,
            fullNodeText: fullNodeText,
            startOffset: startOffset,
            endOffset: endOffset,
            selectionRange: [
              selectionRange[0] as int,
              selectionRange[1] as int,
            ],
          ).timeout(
            const Duration(seconds: 3),
            onTimeout: () {
              AnxLog.warning('Segmentation data storage timed out');
            },
          );

          return true;
        } catch (e) {
          AnxLog.severe('Error storing segmentation data: $e');
          return false;
        }
      },
    );

    // Word boundaries handler removed - segmentation mode no longer supported

    controller.addJavaScriptHandler(
      handlerName: 'onLoadEnd',
      callback: (args) {
        widget.onLoadEnd();

        // Preemptively process visible Chinese text
        preprocessVisibleContent();

        // Add a delayed annotation rendering to ensure highlights persist after book reopening
        Future.delayed(const Duration(milliseconds: 500), () {
          if (mounted) {
            AnxLog.info(
              'Delayed annotation rendering for highlight persistence',
            );
            renderAnnotations(controller);
          }
        });
      },
    );

    controller.addJavaScriptHandler(
      handlerName: 'onRelocated',
      callback: (List<dynamic> args) {
        final Map<String, dynamic> location = args[0] as Map<String, dynamic>;
        if (cfi == location['cfi']) return;
        setState(() {
          cfi = location['cfi'] as String? ?? '';
          percentage = (location['percentage'] as num?)?.toDouble() ?? 0.0;
          chapterTitle = location['chapterTitle'] as String? ?? '';
          chapterHref = location['chapterHref'] as String? ?? '';
          chapterCurrentPage = (location['chapterCurrentPage'] as int?) ?? 0;
          chapterTotalPages = (location['chapterTotalPages'] as int?) ?? 0;
          bookmarkExists = (location['bookmark']?['exists'] as bool?) ?? false;
          bookmarkCfi = (location['bookmark']?['cfi'] as String?) ?? '';
        });
        widget.updateParent();
        saveReadingProgress();
        readingPageKey.currentState?.resetAwakeTimer();

        // Preemptively process visible Chinese text when page changes
        preprocessVisibleContent();

        // Ensure annotations remain visible after page navigation
        Future.delayed(const Duration(milliseconds: 200), () {
          if (mounted) {
            renderAnnotations(controller);
          }
        });
      },
    );
    controller.addJavaScriptHandler(
      handlerName: 'onClick',
      callback: (List<dynamic> args) {
        final Map<String, dynamic> location = args[0] as Map<String, dynamic>;
        onClick(location);
      },
    );
    controller.addJavaScriptHandler(
      handlerName: 'onSetToc',
      callback: (List<dynamic> args) {
        final List<dynamic> t = args[0] as List<dynamic>;
        toc =
            t.map((i) => TocItem.fromJson(i as Map<String, dynamic>)).toList();
      },
    );
    controller.addJavaScriptHandler(
      handlerName: 'onSelectionEnd',
      callback: (List<dynamic> args) {
        try {
          removeOverlay();

          // Enhanced parameter validation
          if (args.isEmpty) {
            AnxLog.warning('onSelectionEnd called with empty arguments');
            return;
          }

          final Map<String, dynamic> location = args[0] as Map<String, dynamic>;
          final String cfi = location['cfi'] as String? ?? '';
          final String text = (location['text'] as String? ?? '').trim();
          final bool footnote = location['footnote'] as bool? ?? false;

          // Validate essential data
          if (text.isEmpty) {
            AnxLog.info('Selection text is empty, not showing context menu');
            return;
          }

          final Map<String, dynamic> pos =
              location['pos'] as Map<String, dynamic>? ?? {};
          final Map<String, dynamic> point =
              pos['point'] as Map<String, dynamic>? ?? {};

          // Enhanced position validation
          final double x = (point['x'] as num?)?.toDouble() ?? 0.0;
          final double y = (point['y'] as num?)?.toDouble() ?? 0.0;

          // Validate position coordinates
          if (x.isNaN || y.isNaN || x < 0 || y < 0) {
            AnxLog.warning('Invalid position coordinates: x=$x, y=$y');
            return;
          }

          final String dir = pos['dir'] as String? ?? '';

          // Log selection for debugging (free selection mode only)
          AnxLog.info(
            'Showing context menu for text: "${text.length > 20 ? '${text.substring(0, 20)}...' : text}" (free selection mode)',
          );

          showUnifiedContextMenu(context, x, y, dir, text, cfi, null, footnote);
        } catch (e) {
          AnxLog.severe('Error in onSelectionEnd handler: $e');
          // Don't show context menu if there's an error to prevent crashes
        }
      },
    );
    controller.addJavaScriptHandler(
      handlerName: 'onAnnotationClick',
      callback: (List<dynamic> args) {
        final Map<String, dynamic> annotation = args[0] as Map<String, dynamic>;
        final Map<String, dynamic> annotationData =
            annotation['annotation'] as Map<String, dynamic>? ?? {};
        final int id = annotationData['id'] as int? ?? 0;
        final String cfi = annotationData['value'] as String? ?? '';
        final String note = annotationData['note'] as String? ?? '';
        final Map<String, dynamic> pos =
            annotation['pos'] as Map<String, dynamic>? ?? {};
        final Map<String, dynamic> point =
            pos['point'] as Map<String, dynamic>? ?? {};
        final double x = (point['x'] as num?)?.toDouble() ?? 0.0;
        final double y = (point['y'] as num?)?.toDouble() ?? 0.0;
        final String dir = pos['dir'] as String? ?? '';
        showUnifiedContextMenu(context, x, y, dir, note, cfi, id, false);
      },
    );
    controller.addJavaScriptHandler(
      handlerName: 'onSearch',
      callback: (List<dynamic> args) {
        final Map<String, dynamic> search = args[0] as Map<String, dynamic>;
        setState(() {
          if (search['process'] != null) {
            searchProcess = (search['process'] as num?)?.toDouble() ?? 0.0;
            _searchProgressController.add(searchProcess);
          } else {
            searchResult.add(SearchResultModel.fromJson(search));
            _searchResultController.add(searchResult);
          }
        });
      },
    );
    controller.addJavaScriptHandler(
      handlerName: 'renderAnnotations',
      callback: (args) {
        renderAnnotations(controller);
      },
    );
    controller.addJavaScriptHandler(
      handlerName: 'onPushState',
      callback: (List<dynamic> args) {
        final Map<String, dynamic> state = args[0] as Map<String, dynamic>;
        canGoBack = state['canGoBack'] as bool? ?? false;
        canGoForward = state['canGoForward'] as bool? ?? false;
        if (!mounted) return;
        setState(() {
          showHistory = true;
        });
        Future.delayed(const Duration(seconds: 20), () {
          if (!mounted) return;
          setState(() {
            showHistory = false;
          });
        });
      },
    );
    controller.addJavaScriptHandler(
      handlerName: 'onImageClick',
      callback: (List<dynamic> args) {
        final String image = args[0] as String? ?? '';
        Navigator.push(
          context,
          MaterialPageRoute<void>(
            builder: (context) => ImageViewer(
              image: image,
              bookName: widget.book.title,
            ),
          ),
        );
      },
    );

    // iOS-specific error handler for EPUB loading issues
    controller.addJavaScriptHandler(
      handlerName: 'onLoadError',
      callback: (List<dynamic> args) {
        final Map<String, dynamic> errorData = args[0] as Map<String, dynamic>;
        final String error = errorData['error'] as String? ?? 'Unknown error';
        final String url = errorData['url'] as String? ?? 'Unknown URL';
        final String platform =
            errorData['platform'] as String? ?? 'Unknown platform';

        AnxLog.severe('$platform EPUB Load Error: $error for URL: $url');

        if (platform == 'iOS') {
          AnxLog.severe(
            'iOS: EPUB rendering failed - this is the critical issue causing blank reading area',
          );
          // You can add user notification here if needed
        }
      },
    );
    controller.addJavaScriptHandler(
      handlerName: 'onFootnoteClose',
      callback: (List<dynamic> args) {
        removeOverlay();
      },
    );
    controller.addJavaScriptHandler(
      handlerName: 'handleBookmark',
      callback: (List<dynamic> args) async {
        final Map<String, dynamic> data = args[0] as Map<String, dynamic>;
        final Map<String, dynamic> detail =
            data['detail'] as Map<String, dynamic>;
        final bool remove = data['remove'] as bool? ?? false;
        final String cfi = detail['cfi'] as String? ?? '';
        final double percentage =
            (detail['percentage'] as num?)?.toDouble() ?? 0.0;
        final String content = detail['content'] as String? ?? '';

        if (remove) {
          ref
              .read(bookmarkProvider(widget.book.id).notifier)
              .removeBookmark(cfi: cfi);
          bookmarkCfi = '';
          bookmarkExists = false;
        } else {
          AnxLog.info(
            'Creating bookmark with data: cfi=$cfi, percentage=$percentage, content="$content", chapter="$chapterTitle"',
          );
          BookmarkModel bookmark = await ref
              .read(bookmarkProvider(widget.book.id).notifier)
              .addBookmark(
                BookmarkModel(
                  bookId: widget.book.id,
                  cfi: cfi,
                  percentage: percentage,
                  content: content,
                  chapter: chapterTitle,
                  updateTime: DateTime.now(),
                  createTime: DateTime.now(),
                ),
              );
          bookmarkCfi = cfi;
          bookmarkExists = true;
          addBookmark(bookmark);
        }
        widget.updateParent();
        setState(() {});
      },
    );
  }

  Future<void> onWebViewCreated(InAppWebViewController controller) async {
    if (!kIsWeb && defaultTargetPlatform == TargetPlatform.android) {
      await InAppWebViewController.setWebContentsDebuggingEnabled(true);
    }
    webViewController = controller;
    setHandler(controller);
  }

  void removeOverlay() {
    if (contextMenuEntry == null || contextMenuEntry?.mounted == false) return;
    contextMenuEntry?.remove();
    contextMenuEntry = null;
  }

  void _handleKeyAndMouseEvents(KeyEvent event) {
    final nextPageEvent = [
      LogicalKeyboardKey.arrowRight,
      LogicalKeyboardKey.arrowDown,
      LogicalKeyboardKey.pageDown,
      LogicalKeyboardKey.space,
    ];

    final prevPageEvent = [
      LogicalKeyboardKey.arrowLeft,
      LogicalKeyboardKey.arrowUp,
      LogicalKeyboardKey.pageUp,
    ];

    final appBarEvent = [
      LogicalKeyboardKey.enter,
    ];

    if (event is KeyDownEvent) {
      if (nextPageEvent.contains(event.logicalKey)) {
        nextPage();
      } else if (prevPageEvent.contains(event.logicalKey)) {
        prevPage();
      } else if (appBarEvent.contains(event.logicalKey)) {
        widget.showOrHideAppBarAndBottomBar(true);
      }
    }
  }

  Future<void> _handlePointerEvents(PointerEvent event) async {
    if (await isFootNoteOpen() || Prefs().pageTurnStyle == PageTurn.scroll) {
      return;
    }
    if (event is PointerScrollEvent) {
      if (event.scrollDelta.dy > 0) {
        nextPage();
      } else {
        prevPage();
      }
    }
  }

  @override
  void initState() {
    book = widget.book;
    focusNode.requestFocus();

    contextMenu = ContextMenu(
      settings: ContextMenuSettings(hideDefaultSystemContextMenuItems: true),
      onCreateContextMenu: (hitTestResult) async {
        // webViewController.evaluateJavascript(source: "showContextMenu()");
      },
      onHideContextMenu: () {
        // removeOverlay();
      },
    );
    if (Prefs().openBookAnimation) {
      _animationController = AnimationController(
        duration: DesignSystem.durationSlow + const Duration(milliseconds: 100),
        vsync: this,
      );
      _animation =
          Tween<double>(begin: 1.0, end: 0.0).animate(_animationController!);
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _animationController!.forward();
      });
    }

    super.initState();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }

  Future<void> saveReadingProgress() async {
    if (cfi == '') return;
    Book book = widget.book;
    book.lastReadPosition = cfi;
    book.readingPercentage = percentage;
    await updateBook(book);
    if (mounted) {
      ref.read(bookListProvider.notifier).refresh();
    }
  }

  @override
  void dispose() {
    // Cancel timers to prevent memory leaks
    styleTimer?.cancel();

    // Dispose of animation controller
    _animationController?.dispose();

    // Dispose of stream controllers to prevent memory leaks
    _searchProgressController.close();
    _searchResultController.close();

    // Dispose of focus node
    focusNode.dispose();

    // Clear web view cache on mobile platforms
    if (defaultTargetPlatform == TargetPlatform.android ||
        defaultTargetPlatform == TargetPlatform.iOS ||
        defaultTargetPlatform == TargetPlatform.macOS) {
      InAppWebViewController.clearAllCache();
    }

    // Save progress and clean up overlay
    saveReadingProgress();
    removeOverlay();

    super.dispose();
  }

  InAppWebViewSettings initialSettings = InAppWebViewSettings(
    supportZoom: false,
    transparentBackground: true,
    isInspectable: kDebugMode,
    // iOS-specific: Enable text selection and interaction
    allowsLinkPreview: false,
    allowsBackForwardNavigationGestures: false,
    // Ensure text selection works on iOS
    disallowOverScroll: false,
  );

  void changeReadingInfo() {
    setState(() {});
  }

  Widget readingInfoWidget() {
    if (chapterCurrentPage == 0) {
      return const SizedBox();
    }

    // Use WCAG AAA compliant colors for reading info overlay
    TextStyle textStyle = TextStyle(
      color: DesignSystem.getSettingsTextColor(context, isPrimary: false),
      fontSize: DesignSystem.getAdjustedFontSize(
        context,
        DesignSystem.fontSizeXS,
      ),
    );

    Widget chapterTitleWidget = Text(
      (chapterCurrentPage == 1 ? widget.book.title : chapterTitle),
      style: textStyle,
    );

    Widget chapterProgressWidget = Text(
      '$chapterCurrentPage/$chapterTotalPages',
      style: textStyle,
    );

    Widget bookProgressWidget =
        Text('${(percentage * 100).toStringAsFixed(2)}%', style: textStyle);

    Widget timeWidget() => StreamBuilder<void>(
          stream: Stream<void>.periodic(const Duration(seconds: 1)),
          builder: (context, snapshot) {
            String currentTime = DateFormat('HH:mm').format(DateTime.now());
            return Text(currentTime, style: textStyle);
          },
        );

    Widget batteryWidget = FutureBuilder(
      future: Battery().batteryLevel,
      builder: (context, snapshot) {
        if (snapshot.hasData) {
          return Stack(
            alignment: Alignment.center,
            children: [
              Padding(
                padding: const EdgeInsets.fromLTRB(0, 0.8, 2, 0),
                child: Text(
                  '${snapshot.data}',
                  style: TextStyle(
                    color: DesignSystem.getSettingsTextColor(
                      context,
                      isPrimary: true,
                    ),
                    fontSize: DesignSystem.getAdjustedFontSize(
                      context,
                      DesignSystem.fontSizeXS,
                    ),
                  ),
                ),
              ),
              Icon(
                HeroIcons.battery_0,
                size: DesignSystem.getAdjustedIconSize(27),
                color: DesignSystem.getSettingsTextColor(
                  context,
                  isPrimary: true,
                ),
              ),
            ],
          );
        } else {
          return const SizedBox();
        }
      },
    );

    Widget batteryAndTimeWidget() => Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            batteryWidget,
            const SizedBox(width: 5),
            timeWidget(),
          ],
        );

    Widget getWidget(ReadingInfoEnum readingInfoEnum) {
      switch (readingInfoEnum) {
        case ReadingInfoEnum.chapterTitle:
          return chapterTitleWidget;
        case ReadingInfoEnum.chapterProgress:
          return chapterProgressWidget;
        case ReadingInfoEnum.bookProgress:
          return bookProgressWidget;
        case ReadingInfoEnum.battery:
          return batteryWidget;
        case ReadingInfoEnum.time:
          return timeWidget();
        case ReadingInfoEnum.batteryAndTime:
          return batteryAndTimeWidget();
        case ReadingInfoEnum.none:
          return const SizedBox();
      }
    }

    List<Widget> headerWidgets = [
      getWidget(Prefs().readingInfo.headerLeft),
      getWidget(Prefs().readingInfo.headerCenter),
      getWidget(Prefs().readingInfo.headerRight),
    ];

    List<Widget> footerWidgets = [
      getWidget(Prefs().readingInfo.footerLeft),
      getWidget(Prefs().readingInfo.footerCenter),
      getWidget(Prefs().readingInfo.footerRight),
    ];

    return Container(
      padding: const EdgeInsets.fromLTRB(20, 10, 20, 30),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SafeArea(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: headerWidgets,
            ),
          ),
          const Spacer(),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: footerWidgets,
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    String uri = Uri.encodeComponent(widget.book.fileFullPath);
    String url = 'http://127.0.0.1:${Server().port}/book/$uri';
    String initialCfi = widget.cfi ?? widget.book.lastReadPosition;

    return KeyboardListener(
      focusNode: focusNode,
      onKeyEvent: _handleKeyAndMouseEvents,
      child: Listener(
        onPointerSignal: (event) {
          _handlePointerEvents(event);
        },
        child: Scaffold(
          resizeToAvoidBottomInset: false,
          body: Stack(
            children: [
              SizedBox.expand(
                child: InAppWebView(
                  webViewEnvironment: webViewEnvironment,
                  initialUrlRequest: URLRequest(
                    url: WebUri(
                      generateUrl(
                        url,
                        initialCfi,
                        backgroundColor: backgroundColor,
                        textColor: textColor,
                      ),
                    ),
                  ),
                  initialSettings: initialSettings,
                  contextMenu: contextMenu,
                  onLoadStop: (controller, url) => onWebViewCreated(controller),
                  onConsoleMessage: webviewConsoleMessage,
                ),
              ),
              readingInfoWidget(),
              if (showHistory)
                Positioned(
                  bottom: 30,
                  left: 0,
                  child: Container(
                    width: MediaQuery.of(context).size.width,
                    padding: const EdgeInsets.all(10),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        if (canGoBack)
                          SemanticHelpers.button(
                            context: context,
                            label: 'Previous page',
                            hint:
                                'Navigate back to previous page in reading history',
                            onTap: () {
                              backHistory();
                            },
                            child: IconButton(
                              onPressed: () {
                                backHistory();
                              },
                              icon: const Icon(Icons.arrow_back_ios),
                            ),
                          ),
                        if (canGoForward)
                          SemanticHelpers.button(
                            context: context,
                            label: 'Next page',
                            hint:
                                'Navigate forward to next page in reading history',
                            onTap: () {
                              forwardHistory();
                            },
                            child: IconButton(
                              onPressed: () {
                                forwardHistory();
                              },
                              icon: const Icon(Icons.arrow_forward_ios),
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              if (Prefs().openBookAnimation)
                SizedBox.expand(
                  child: Prefs().openBookAnimation
                      ? IgnorePointer(
                          ignoring: true,
                          child: FadeTransition(
                            opacity: _animation!,
                            child: bookCover(context, widget.book),
                          ),
                        )
                      : bookCover(context, widget.book),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
