/// Enum for text selection modes in the ebook reader
/// Note: Only free selection mode is supported
enum TextSelectionMode {
  /// Free selection mode - users can freely select any text portion
  free;

  /// Get the display name for the selection mode
  String get displayName {
    switch (this) {
      case TextSelectionMode.free:
        return 'Free Selection';
    }
  }

  /// Get the mode from string name
  static TextSelectionMode fromString(String name) {
    // Always return free selection since it's the only supported mode
    return TextSelectionMode.free;
  }
}
