import 'dart:async';
import 'package:dasso_reader/utils/log/common.dart';

/// A service for Chinese text context menu data generation
/// Note: Word boundary detection for text selection has been removed
class ChineseSegmentationService {
  static final ChineseSegmentationService _instance =
      ChineseSegmentationService._internal();

  /// Singleton instance
  factory ChineseSegmentationService() => _instance;

  ChineseSegmentationService._internal();

  /// Flag to track initialization status
  bool _isInitialized = false;

  /// Cache for storing comprehensive segmentation data for context menu
  final _contextMenuSegmentationCache =
      _LRUCache<String, Map<String, dynamic>>(50);

  /// Completer for initialization to handle concurrent calls
  static Completer<void>? _initCompleter;

  /// Initialize the segmentation service
  Future<void> initialize() async {
    // Quick check without lock
    if (_isInitialized) return;

    // Use completer-based synchronization to handle concurrent calls
    if (_initCompleter != null) {
      // Another initialization is in progress, wait for it
      return await _initCompleter!.future;
    }

    // Create completer and start initialization
    _initCompleter = Completer<void>();

    try {
      AnxLog.info(
        'Initializing Chinese segmentation service (context menu only)',
      );

      // Double-check after acquiring the completer
      if (_isInitialized) {
        _initCompleter!.complete();
        return;
      }

      // No external dependencies needed - just mark as initialized
      _isInitialized = true;
      AnxLog.info(
        'Chinese segmentation service initialized (context menu only)',
      );
      _initCompleter!.complete();
    } catch (e) {
      AnxLog.severe('Failed to initialize Chinese segmentation service: $e');
      _initCompleter!.complete();
    } finally {
      _initCompleter = null;
    }
  }

  // Word boundary detection methods removed - only context menu functionality remains

  /// Store comprehensive segmentation data for context menu use
  Future<void> storeSegmentationDataForSelection({
    required String selectedText,
    required String fullNodeText,
    required int startOffset,
    required int endOffset,
    required List<int> selectionRange,
    required int bookId,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      final Stopwatch stopwatch = Stopwatch()..start();

      // Generate comprehensive segmentation data for the context menu
      final segmentationData = await _generateComprehensiveSegmentationData(
        selectedText: selectedText,
        fullNodeText: fullNodeText,
        startOffset: startOffset,
        endOffset: endOffset,
        selectionRange: selectionRange,
      );

      // Store in cache with a unique key
      final cacheKey = '${bookId}_${startOffset}_${endOffset}_$selectedText';
      _contextMenuSegmentationCache.set(cacheKey, segmentationData);

      final elapsed = stopwatch.elapsedMilliseconds;
      AnxLog.info(
        'Stored comprehensive segmentation data for context menu in ${elapsed}ms',
      );
    } catch (e) {
      AnxLog.severe('Error storing segmentation data for selection: $e');
    }
  }

  /// Generate simplified segmentation data for context menu
  /// Note: This now provides basic character-level segmentation without word boundary detection
  Future<Map<String, dynamic>> _generateComprehensiveSegmentationData({
    required String selectedText,
    required String fullNodeText,
    required int startOffset,
    required int endOffset,
    required List<int> selectionRange,
  }) async {
    final segmentationData = <String, dynamic>{};

    try {
      // Generate basic character-level and simple word combinations for context menu
      final possibleWords = <Map<String, dynamic>>[];

      // Add the selected text itself
      possibleWords.add({
        'word': selectedText,
        'start': startOffset,
        'end': endOffset,
        'isExactMatch': true,
        'containsSelection': true,
        'overlapsSelection': true,
      });

      // Add individual characters from the selection for Chinese text
      if (_isChinese(selectedText)) {
        for (int i = 0; i < selectedText.length; i++) {
          final char = selectedText[i];
          if (_isChinese(char) && char != selectedText) {
            possibleWords.add({
              'word': char,
              'start': startOffset + i,
              'end': startOffset + i + 1,
              'isExactMatch': false,
              'containsSelection': false,
              'overlapsSelection': true,
            });
          }
        }
      }

      // Generate simple segmentation options (character-level)
      final allSegmentationOptions = <Map<String, dynamic>>[];

      // Add character-by-character segmentation
      for (int i = 0; i < selectedText.length; i++) {
        final char = selectedText[i];
        if (char.trim().isNotEmpty) {
          allSegmentationOptions.add({
            'word': char,
            'direction': 'character-level',
            'type': 'segmentation',
          });
        }
      }

      // Remove duplicates
      final uniqueWords = <String, Map<String, dynamic>>{};
      for (final option in allSegmentationOptions) {
        final word = option['word'] as String;
        if (!uniqueWords.containsKey(word)) {
          uniqueWords[word] = option;
        }
      }

      segmentationData['selectedText'] = selectedText;
      segmentationData['fullNodeText'] = fullNodeText;
      segmentationData['selectionRange'] = selectionRange;
      segmentationData['possibleWords'] = possibleWords;
      segmentationData['segmentationOptions'] = uniqueWords.values.toList();
      segmentationData['timestamp'] = DateTime.now().millisecondsSinceEpoch;

      AnxLog.info(
        'Generated ${possibleWords.length} possible words and ${uniqueWords.length} segmentation options (simplified)',
      );
    } catch (e) {
      AnxLog.severe('Error generating comprehensive segmentation data: $e');
    }

    return segmentationData;
  }

  /// Check if text contains Chinese characters
  bool _isChinese(String text) {
    return RegExp(r'[\u4e00-\u9fa5]').hasMatch(text);
  }

  /// Get stored segmentation data for context menu
  Map<String, dynamic>? getStoredSegmentationData({
    required String selectedText,
    required int startOffset,
    required int endOffset,
    required int bookId,
  }) {
    final cacheKey = '${bookId}_${startOffset}_${endOffset}_$selectedText';
    return _contextMenuSegmentationCache.get(cacheKey);
  }

  // Synchronization methods removed - no longer needed for simplified service
}

/// A simple LRU cache implementation
class _LRUCache<K, V> {
  final int _capacity;
  final Map<K, V> _cache = {};
  final List<K> _keys = [];

  _LRUCache(this._capacity);

  V? get(K key) {
    if (!_cache.containsKey(key)) return null;

    // Move key to the end of the list (most recently used)
    _keys.remove(key);
    _keys.add(key);

    return _cache[key];
  }

  void set(K key, V value) {
    if (_cache.containsKey(key)) {
      // Update existing key
      _cache[key] = value;
      _keys.remove(key);
      _keys.add(key);
    } else {
      // Add new key
      _cache[key] = value;
      _keys.add(key);

      // Remove oldest key if capacity is exceeded
      if (_keys.length > _capacity) {
        final oldestKey = _keys.removeAt(0);
        _cache.remove(oldestKey);
      }
    }
  }

  void clear() {
    _cache.clear();
    _keys.clear();
  }
}
